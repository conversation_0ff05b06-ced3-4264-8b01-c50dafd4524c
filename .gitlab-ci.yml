# Shared YAML config pulled from another repo
include:
  - project: 'son-video/support'
    ref: master
    file: '/.gitlab-ci-template.yml'

workflow:
  rules:
    # pipeline should only run for merge requests, scheduled pipelines or triggered manually from the Gitlab CI pipeline page
    - if: $CI_PIPELINE_SOURCE =~ /^merge_request_event|schedule|web$/
    - if: $CI_COMMIT_BRANCH =~ /^(master|validation)$/

default:
  image: $CI_REGISTRY/son-video/clippy/test:latest
  interruptible: true
  tags:
    - saas-linux-small-amd64

variables:
  MONGO_PASSWORD: root
  # Filesystem for docker jobs
  DOCKER_DRIVER: overlay2
  # Files shared between jobs
  ARTIFACTS_DIR: $CI_PROJECT_DIR/artifacts
  CI_TESTS_STAGE:
    value: 'EXECUTE'
    description: "Indicates the action to do regarding tests"
    options:
      - 'EXECUTE'
      - 'SKIP'
  PYTHON_DEPENDENCIES:
    value: 'DEFAULT'
    description: "REGENERATE the CI rule regarding the generation of the python Dependencies (cached by default)"
    options:
      - 'DEFAULT'
      - 'REGENERATE'

stages:
  - install
  - test
  - build-deploy
  - deploy

# same requirements files hash should always use the same vendors
.cache-pip: &cache_pip
  key:
    files:
      - requirements.txt
      - requirements-dev.txt
  paths:
    - var/cache/packages

.run-test:
  rules:
    - if: $CI_TESTS_STAGE != "SKIP" && $CI_COMMIT_BRANCH != "validation"

#####################################
# Install Stage
#####################################

install-python-dependencies:
  stage: install
  rules:
    - if: PYTHON_DEPENDENCIES == "REGENERATE"
    # trigger this job only if requirements files has changed, or manual (see ruleschanges in doc)
    - changes:
      - requirements.txt
      - requirements-dev.txt
  extends:
    - .with-ssh
  cache:
    - <<: *cache_pip
      policy: pull-push
    # store dependency managers' caches for all branches
    - key: ${CI_JOB_NAME}
      # must be inside $CI_PROJECT_DIR for gitlab-runner caching
      paths:
        - var/cache/packages
      policy: pull-push
  script:
    # exit if cache already contains the .venv dir
    - if [[ "PYTHON_DEPENDENCIES" != "REGENERATE" ]]; then test -d .venv && exit 10; fi
    - if [[ "PYTHON_DEPENDENCIES" == "REGENERATE" ]]; then echo "REGENERATE has been forced from the UI"; fi
    # install
    - pip install -r requirements-dev.txt
  allow_failure:
    exit_codes: 10


#####################################
# Test Stage
#####################################

# build-test not necessary

.common-test:
  extends:
    - .with-ssh
    - .run-test
  cache:
    - <<: *cache_pip
      policy: pull

unit-tests:
  stage: test
  extends: .common-test
  script:
    # Install dependencies from cache
    - pip install -r requirements-dev.txt
    # Run test
    - python -m pytest

#####################################
# Build-deploy Stage
#####################################

build-deploy:
  stage: build-deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "validation"
    - if: $CI_COMMIT_BRANCH == "master"
  cache:
    - <<: *cache_pip
      policy: pull # "pip install -r requirements.txt" will update the local directory, but we don't want to cache (push) that
  script:
    - mkdir -p $ARTIFACTS_DIR/deploy
    - apt-get update && apt-get install -y make
    - cd deployment && make package && cd -

    # copy newly generated dependencies in artifacts for deployment in next recipe
    - cp -r deployment/clippy.tar $ARTIFACTS_DIR/deploy/clippy.tar
  artifacts:
    expire_in: 1 day
    paths:
      - $ARTIFACTS_DIR/


#####################################
# Deploy Stage
#####################################
.deployment:
  stage: deploy
  extends:
    - .with-ssh
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  interruptible: true
  script: &deployment_scripts
    - apt-get update && apt-get install -y make git

    - git config --global user.name "${GITLAB_USER_NAME}"
    - git config --global user.email "${GITLAB_USER_EMAIL}"
    - git remote set-url --<NAME_EMAIL>:son-video/clippy.git
    - git fetch -p --all --tags --force

    - cp -r $ARTIFACTS_DIR/deploy/clippy.tar deployment/clippy.tar

deploy-validation:
  rules:
    - if: $CI_COMMIT_BRANCH == "validation"
  extends: .deployment
  script:
    - *deployment_scripts
    - cd deployment && make deploy-validation

deploy-staging:
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
  extends: .deployment
  script:
    - *deployment_scripts
    - cd deployment && make deploy
