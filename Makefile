INCLUDE_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))/.phcstack

include $(INCLUDE_DIR)/with-deploy.mk
include $(INCLUDE_DIR)/with-registry.mk
include $(INCLUDE_DIR)/base.mk

.PHONY: install
install: my-before-install pip-install start ##@ Installation des dépendances (et lance le service)

my-before-install: purge create-local-volume create-volumes ## Common action to run before installing the app

exec-help::
	@printf " $(INFO_COLOR)Tests$(NO_COLOR) $(OK_COLOR)❯$(NO_COLOR) $(WARN_COLOR) python -m pytest$(NO_COLOR)\n"

.PHONY: create-local-volume
create-local-volume: ## create local volume with correct permissions
	mkdir -p var/data/mongo-db

.PHONY: pip-install
pip-install: ## install python requirements
	@$(DOCKER_COMPOSE) run --rm --user $(USER_ID) -i $(EXEC_CONTAINER_NAME) python -m venv /var/www/clippy/.venv
	@$(DOCKER_COMPOSE) run --rm --user $(USER_ID) -i $(EXEC_CONTAINER_NAME) pip install -r requirements-dev.txt

.PHONY: run
run: ##@ Ouvre une session CLI sur l'image, sans lancer le service
	@$(DOCKER_COMPOSE) run --rm -it $(EXEC_CONTAINER_NAME) bash

.PHONY: required-dependencies
required-dependencies:: ##@ Démarre les dépendances requises (mais ne les installent pas).
	@bash $(BASE_DIR)/.phcstack/check_dependency.sh Traefik traefik traefik
