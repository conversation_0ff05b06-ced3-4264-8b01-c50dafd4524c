# Clippy

Chatbot server

## Install

Créer un fichier `.env.local` et ajouter l'entrée `OPENAI_API_KEY` (cf. fichier .env)

Lancer la commande suivante :
```bash
phcstack clippy install
```

## Configuration de PyCharm Community Edition

- Installer un interpreteur local python :
```bash
apt install python3
```

Dand PyCharm, configurer un interpreteur python local :
- <PERSON>er dans `File > Settings > Python > Interpreter`
- C<PERSON>r sur `Add Interpreter > Add local interpreter`
- Choisir l'interpreteur python3 installé précédemment
- Choi<PERSON><PERSON>z une 'Location' différente (ex: .host_venv)
